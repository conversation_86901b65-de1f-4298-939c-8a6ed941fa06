const express = require('express');
const kafkaClient = require('../kafka/dynamicKafkaClient');
const logger = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');
const {
  requirePermission,
  filterTopicsByAccess,
  PERMISSIONS
} = require('../middleware/rbac');
const { rbacService } = require('../services/rbacService');

const router = express.Router();

// GET /api/consumers - List consumer groups based on user access
router.get('/', authenticateToken, filterTopicsByAccess, async (req, res) => {
  try {
    const allConsumerGroups = await kafkaClient.getConsumerGroups();

    // Filter consumer groups based on user's topic access
    const userPermissions = await rbacService.getUserPermissions(req.user.id);
    let accessibleConsumerGroups = [];

    if (userPermissions.role === 'SUPER_ADMIN') {
      accessibleConsumerGroups = allConsumerGroups;
    } else {
      // Filter consumer groups based on topics they consume
      for (const group of allConsumerGroups) {
        if (await rbacService.hasConsumerGroupAccess(req.user.id, group)) {
          accessibleConsumerGroups.push(group);
        }
      }
    }

    res.json({
      success: true,
      data: accessibleConsumerGroups,
      count: accessibleConsumerGroups.length
    });
  } catch (error) {
    logger.error('Error fetching consumer groups:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch consumer groups',
        status: 500
      }
    });
  }
});

// GET /api/consumers/:groupId - Get consumer group details
router.get('/:groupId', authenticateToken, async (req, res) => {
  try {
    const { groupId } = req.params;
    const groupDetails = await kafkaClient.getConsumerGroupDetails(groupId);
    res.json({
      success: true,
      data: groupDetails
    });
  } catch (error) {
    logger.error('Error fetching consumer group details:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch consumer group details',
        status: 500
      }
    });
  }
});

// DELETE /api/consumers/:groupId - Delete consumer group (requires permission and topic access validation)
router.delete('/:groupId', authenticateToken, requirePermission(PERMISSIONS.DELETE_CONSUMER_GROUPS), async (req, res) => {
  try {
    const { groupId } = req.params;

    // For non-super-admin users, validate they have access to topics consumed by this group
    const userPermissions = await rbacService.getUserPermissions(req.user.id);
    if (userPermissions.role !== 'SUPER_ADMIN') {
      // Get consumer group details to check topics it consumes
      const consumerGroup = await kafkaClient.getConsumerGroupDetails(groupId);

      // Check if user has access to any topic this consumer group consumes
      let hasAccess = false;
      if (consumerGroup.topics && consumerGroup.topics.length > 0) {
        for (const topic of consumerGroup.topics) {
          if (await rbacService.hasTopicAccess(req.user.id, topic)) {
            hasAccess = true;
            break;
          }
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: You do not have permission to delete this consumer group'
        });
      }
    }

    const result = await kafkaClient.deleteConsumerGroup(groupId);
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error deleting consumer group:', error);
    res.status(500).json({
      error: {
        message: 'Failed to delete consumer group',
        status: 500
      }
    });
  }
});

module.exports = router; 