import React, { useState } from 'react';
import { usePermissions, PERMISSIONS } from '../contexts/PermissionsContext';
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Paper,
  useTheme,
  useMediaQuery,
  Autocomplete
} from '@mui/material';
import {
  Send,
  Clear,
} from '@mui/icons-material';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
import { useTopics } from '../hooks/useTopics';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`producer-tabpanel-${index}`}
    aria-labelledby={`producer-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const Producer = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedTopic, setSelectedTopic] = useState('');
  const [messageKey, setMessageKey] = useState('');
  const [messageValue, setMessageValue] = useState('');
  const [headers, setHeaders] = useState('{}');

  const { hasPermission, getAccessibleTopics, loading: permissionsLoading } = usePermissions();
  const [messageHistory, setMessageHistory] = useState([]);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: topics, isLoading: topicsLoading } = useTopics();

  // Filter topics based on user permissions
  const accessibleTopics = getAccessibleTopics(topics || []);

  // Check if user has permission to produce messages
  if (permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!hasPermission(PERMISSIONS.PRODUCE_MESSAGES)) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Access denied. You do not have permission to produce messages.
        </Alert>
      </Box>
    );
  }

  const produceMutation = useMutation(
    ({ topic, message }) => topicsApi.produceMessage(topic, message),
    {
      onSuccess: (data) => {
        toast.success('Message sent successfully');
        // Add to message history
        setMessageHistory(prev => [{
          id: Date.now(),
          topic: selectedTopic,
          key: messageKey,
          value: messageValue,
          timestamp: new Date().toISOString(),
          status: 'success'
        }, ...prev.slice(0, 19)]); // Keep only last 20 messages
        // Clear form
        setMessageKey('');
        setMessageValue('');
      },
      onError: (error) => {
        toast.error(`Error sending message: ${error.message}`);
        setMessageHistory(prev => [{
          id: Date.now(),
          topic: selectedTopic,
          key: messageKey,
          value: messageValue,
          timestamp: new Date().toISOString(),
          status: 'error',
          error: error.message
        }, ...prev.slice(0, 19)]);
      },
    }
  );

  const handleSendMessage = () => {
    if (!selectedTopic) {
      toast.error('Please select a topic');
      return;
    }
    if (!messageValue.trim()) {
      toast.error('Message value is required');
      return;
    }

    let parsedHeaders = {};
    if (headers.trim()) {
      try {
        parsedHeaders = JSON.parse(headers);
      } catch (error) {
        toast.error('Invalid JSON format for headers');
        return;
      }
    }

    const message = {
      key: messageKey || null,
      value: messageValue,
      headers: parsedHeaders,
    };

    produceMutation.mutate({ topic: selectedTopic, message });
  };

  const handleClearForm = () => {
    setMessageKey('');
    setMessageValue('');
    setHeaders('{}');
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  if (topicsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Message Producer
      </Typography>

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Send Message" />
          <Tab label="Message History" />
        </Tabs>
      </Paper>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Compose Message
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  <Autocomplete
                    fullWidth
                    options={accessibleTopics}
                    getOptionLabel={(option) => `${option.name} (${option.partitions} partitions)`}
                    value={accessibleTopics.find(t => t.name === selectedTopic) || null}
                    onChange={(event, newValue) => {
                      setSelectedTopic(newValue ? newValue.name : '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Topic"
                        placeholder="Search topics..."
                        required
                        variant="outlined"
                        size={isSmallScreen ? "small" : "medium"}
                      />
                    )}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <Box>
                          <Typography variant="body1" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            {option.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                            {option.partitions} partitions
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    filterOptions={(options, { inputValue }) =>
                      options.filter(option =>
                        option.name.toLowerCase().includes(inputValue.toLowerCase())
                      )
                    }
                    noOptionsText="No topics found"
                  />

                  <TextField
                    fullWidth
                    label="Message Key (optional)"
                    value={messageKey}
                    onChange={(e) => setMessageKey(e.target.value)}
                    placeholder="Enter message key"
                  />

                  <TextField
                    fullWidth
                    label="Message Value"
                    value={messageValue}
                    onChange={(e) => setMessageValue(e.target.value)}
                    placeholder="Enter message content"
                    multiline
                    rows={6}
                    required
                  />

                  <TextField
                    fullWidth
                    label="Headers (JSON format)"
                    value={headers}
                    onChange={(e) => setHeaders(e.target.value)}
                    placeholder='{"header1": "value1", "header2": "value2"}'
                    multiline
                    rows={3}
                  />

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<Send />}
                      onClick={handleSendMessage}
                      disabled={produceMutation.isLoading}
                    >
                      {produceMutation.isLoading ? 'Sending...' : 'Send Message'}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Clear />}
                      onClick={handleClearForm}
                    >
                      Clear
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Message Preview
                </Typography>
                
                <Box sx={{ backgroundColor: '#f5f5f5', p: 2, borderRadius: 1 }}>
                  <Typography variant="body2" color="textSecondary">
                    Topic: {selectedTopic || 'Not selected'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Key: {messageKey || 'null'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Value: {messageValue || 'Empty'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Headers: {headers || '{}'}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Typography variant="h6" gutterBottom>
          Recent Messages
        </Typography>
        
        {messageHistory.length === 0 ? (
          <Alert severity="info">
            No messages sent yet. Send your first message to see it here.
          </Alert>
        ) : (
          <Grid container spacing={2}>
            {messageHistory.map((message) => (
              <Grid item xs={12} key={message.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6">
                        {message.topic}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="textSecondary">
                          {new Date(message.timestamp).toLocaleString()}
                        </Typography>
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: message.status === 'success' ? 'success.main' : 'error.main',
                          }}
                        />
                      </Box>
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary">
                      Key: {message.key || 'null'}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {message.value}
                    </Typography>
                    
                    {message.status === 'error' && (
                      <Alert severity="error" sx={{ mt: 1 }}>
                        {message.error}
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>
    </Box>
  );
};

export default Producer; 