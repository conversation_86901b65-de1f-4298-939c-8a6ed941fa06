import { authApi } from './api';

const rbacApi = {
  // Get all available roles
  getRoles: async () => {
    return await authApi.getRoles();
  },

  // Get current user permissions
  getUserPermissions: async () => {
    return await authApi.getPermissions();
  },

  // Update user role and topic assignments
  updateUserRole: async (userId, roleData) => {
    return await authApi.updateUserRole(userId, roleData);
  },

  // Register new user with role and topic assignments
  registerUser: async (userData) => {
    return await authApi.register(userData);
  }
};

export default rbacApi;
