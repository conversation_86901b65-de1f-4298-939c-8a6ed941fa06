import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth headers
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response) {
      // Handle auth errors
      if (error.response.status === 401) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // Only redirect if we're not already on the login page
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
      }
      // Server responded with error status
      throw new Error(error.response.data.message || error.response.data.error?.message || 'Server error');
    } else if (error.request) {
      // Request made but no response
      throw new Error('No response from server');
    } else {
      // Something else happened
      throw new Error(error.message || 'Unknown error');
    }
  }
);

// Topics API
export const topicsApi = {
  getAll: (params = {}) => api.get('/topics', { params }),
  // below method currently not using, uncomment for use
  // getAllWithCounts: () => api.get('/topics', { params: { includeCounts: 'true' } }),
  getById: (topicName) => api.get(`/topics/${topicName}`),
  create: (topicData) => api.post('/topics', topicData),
  update: (topicName, configs) => api.put(`/topics/${topicName}`, { configs }),
  delete: (topicName) => api.delete(`/topics/${topicName}`),
  addPartitions: (topicName, partitionCount) => 
    api.post(`/topics/${topicName}/partitions`, { partitionCount }),
  getMessages: (topicName, params) => 
    api.get(`/topics/${topicName}/messages`, { params }),
  searchMessages: (topicName, params) => 
    api.get(`/topics/${topicName}/search`, { params }),
  produceMessage: (topicName, message) => 
    api.post(`/topics/${topicName}/messages`, message),
  subscribe: (topicName) => api.post(`/topics/${topicName}/subscribe`),
  unsubscribe: (topicName) => api.post(`/topics/${topicName}/unsubscribe`),
  // New methods for on-demand message count loading
  getMessageCount: (topicName) => api.get(`/topics/${topicName}/message-count`),
  // Below method currently not using, Uncomment for use
  // getMessageCounts: (topicNames) => api.post('/topics/message-counts', { topicNames }),
};

// Consumer Groups API
export const consumerGroupsApi = {
  getAll: () => api.get('/consumers'),
  getById: (groupId) => api.get(`/consumers/${groupId}`),
  delete: (groupId) => api.delete(`/consumers/${groupId}`),
};

// Producers API
export const producersApi = {
  bulkProduce: (data) => api.post('/producers/bulk', data),
};

// Cluster API
export const clusterApi = {
  getInfo: () => api.get('/cluster/info'),
  getInfoWithEnv: () => api.get('/cluster/info-with-env'),
  getHealth: () => api.get('/cluster/health'),
  getMessageFlow: () => api.get('/cluster/message-flow'),
  getBrokerMetrics: () => api.get('/cluster/broker-metrics'),
  getMessageRate: () => api.get('/cluster/message-rate'),
};

// Config API
export const configApi = {
  get: () => api.get('/config'),
  getKafkaStatus: () => api.get('/config/kafka-status'),
};

// Auth API
export const authApi = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),
  register: (userData) => api.post('/auth/register', userData),
  setup: (adminData) => api.post('/auth/setup', adminData),
  getUsers: () => api.get('/auth/users'),
  updateUser: (userId, userData) => api.put(`/auth/users/${userId}`, userData),
  deleteUser: (userId) => api.delete(`/auth/users/${userId}`),
  getRoles: () => api.get('/auth/roles'),
  getPermissions: () => api.get('/auth/permissions'),
  updateUserRole: (userId, roleData) => api.put(`/auth/users/${userId}/role`, roleData),
};

// Environment API
export const environmentApi = {
  getAvailableEnvironments: () => api.get('/environment/environments'),
  getCurrentEnvironment: () => api.get('/environment/current'),
  switchEnvironment: (environment) => api.post('/environment/switch', { environment }),
  testConnection: (environment) => api.post('/environment/test-connection', { environment }),
};

export default api;