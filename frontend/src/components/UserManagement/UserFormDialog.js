import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
  Chip,
  Autocomplete,
  Alert,
  CircularProgress,
  Stack,
  Divider,
} from '@mui/material';
import { Person, AdminPanelSettings, Edit } from '@mui/icons-material';

const UserFormDialog = ({
  open,
  onClose,
  onSubmit,
  user = null,
  roles = [],
  topics = [],
  isLoading = false,
  title
}) => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    role: 'TOPIC_VIEWER',
    assignedTopics: [],
    hasAllTopicsAccess: false
  });

  const [errors, setErrors] = useState({});
  const isEditing = !!user;

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        email: user.email || '',
        password: '', // Don't populate password for editing
        role: user.role || 'TOPIC_VIEWER',
        assignedTopics: user.assignedTopics || [],
        hasAllTopicsAccess: user.hasAllTopicsAccess || false
      });
    } else {
      setFormData({
        username: '',
        email: '',
        password: '',
        role: 'TOPIC_VIEWER',
        assignedTopics: [],
        hasAllTopicsAccess: false
      });
    }
    setErrors({});
  }, [user, open]);

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleAllTopicsAccessChange = (checked) => {
    setFormData(prev => ({
      ...prev,
      hasAllTopicsAccess: checked,
      assignedTopics: checked ? [] : prev.assignedTopics
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!isEditing && !formData.password.trim()) {
      newErrors.password = 'Password is required';
    }

    if (!formData.role) {
      newErrors.role = 'Role is required';
    }

    // For non-super-admin roles, check topic assignments
    if (formData.role !== 'SUPER_ADMIN' && !formData.hasAllTopicsAccess && formData.assignedTopics.length === 0) {
      newErrors.assignedTopics = 'Please assign at least one topic or enable "All Topics Access"';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    const submitData = {
      username: formData.username.trim(),
      email: formData.email.trim(),
      role: formData.role,
      assignedTopics: formData.hasAllTopicsAccess ? [] : formData.assignedTopics,
      hasAllTopicsAccess: formData.hasAllTopicsAccess
    };

    // Only include password for new users
    if (!isEditing && formData.password.trim()) {
      submitData.password = formData.password;
    }

    onSubmit(submitData);
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return <AdminPanelSettings fontSize="small" />;
      case 'TOPIC_MANAGER':
        return <Edit fontSize="small" />;
      case 'TOPIC_VIEWER':
        return <Person fontSize="small" />;
      default:
        return <Person fontSize="small" />;
    }
  };

  const getRoleDescription = (role) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'Full access to all features and functionality';
      case 'TOPIC_MANAGER':
        return 'Can manage assigned topics, view messages, and produce messages';
      case 'TOPIC_VIEWER':
        return 'Read-only access to assigned topics and messages';
      default:
        return '';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '500px' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getRoleIcon(formData.role)}
          {title || (isEditing ? 'Edit User' : 'Create New User')}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, pt: 1 }}>
          {/* Basic Information */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            <Stack spacing={2}>
              <TextField
                label="Username"
                value={formData.username}
                onChange={(e) => handleChange('username', e.target.value)}
                error={!!errors.username}
                helperText={errors.username}
                fullWidth
                required
              />
              
              <TextField
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                error={!!errors.email}
                helperText={errors.email}
                fullWidth
                required
              />
              
              {!isEditing && (
                <TextField
                  label="Password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                  error={!!errors.password}
                  helperText={errors.password}
                  fullWidth
                  required
                />
              )}
            </Stack>
          </Box>

          <Divider />

          {/* Role Selection */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Role & Permissions
            </Typography>
            <Stack spacing={2}>
              <FormControl fullWidth error={!!errors.role}>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  onChange={(e) => handleChange('role', e.target.value)}
                  label="Role"
                >
                  {roles?.map((role) => (
                    <MenuItem key={role.name} value={role.name}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getRoleIcon(role.name)}
                        {role.displayName}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
                {errors.role && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                    {errors.role}
                  </Typography>
                )}
              </FormControl>

              {formData.role && (
                <Alert severity="info" sx={{ mt: 1 }}>
                  {getRoleDescription(formData.role)}
                </Alert>
              )}
            </Stack>
          </Box>

          {/* Topic Assignment - Only for non-super-admin roles */}
          {formData.role !== 'SUPER_ADMIN' && (
            <>
              <Divider />
              <Box>
                <Typography variant="h6" gutterBottom>
                  Topic Access
                </Typography>
                <Stack spacing={2}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.hasAllTopicsAccess}
                        onChange={(e) => handleAllTopicsAccessChange(e.target.checked)}
                      />
                    }
                    label="Access to All Topics"
                  />

                  {!formData.hasAllTopicsAccess && (
                    <Autocomplete
                      multiple
                      options={topics?.map(topic => topic.name) || []}
                      value={formData.assignedTopics}
                      onChange={(event, newValue) => handleChange('assignedTopics', newValue)}
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={option}
                            {...getTagProps({ index })}
                            key={option}
                          />
                        ))
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Assigned Topics"
                          placeholder="Select topics..."
                          error={!!errors.assignedTopics}
                          helperText={errors.assignedTopics}
                        />
                      )}
                    />
                  )}

                  {formData.hasAllTopicsAccess && (
                    <Alert severity="success">
                      This user will have access to all topics (current and future).
                    </Alert>
                  )}
                </Stack>
              </Box>
            </>
          )}
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button onClick={onClose} disabled={isLoading}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          disabled={isLoading}
          startIcon={isLoading ? <CircularProgress size={16} /> : null}
        >
          {isLoading ? 'Saving...' : (isEditing ? 'Update User' : 'Create User')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserFormDialog;
